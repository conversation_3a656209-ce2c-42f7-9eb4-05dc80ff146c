'use client';

import { <PERSON>ton, <PERSON>over, PopoverTrigger, PopoverContent } from '@heroui/react';

import {
  BankNoteIcon,
  CalendarIcon,
  LocationIcon,
  UserGroupIcon,
  PaletteIcon,
} from '@/components/icons';

import TripFilter from './tripFilter';
import AgeSelector from './AgeSelector';
import TagSelector from './TagSelector';
import PriceRangeSlider from './PriceRangeSlider';
import CustomCalendar from './CustomCalendar';

const PrimaryFilter = () => {
  return (
    <div className="grid grid-cols-5 grid-flow-row w-full bg-white rounded-xl p-2">
      {/* LOCATION - Custom component => Popover */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 justify-start">
            <div className="text-left">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                LOCATION
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <LocationIcon size={20} />
                <p className="text-default-1000 text-sm font-medium">
                  Where to go
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <TripFilter />
        </PopoverContent>
      </Popover>

      {/* Static label block */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start ">
            <div className="border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                NUMBER OF DAY
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <CalendarIcon size={20} />
                <p className="text-default-1000 text-sm font-medium">When is it</p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <CustomCalendar />
        </PopoverContent>
      </Popover>

      {/* TRAVEL STYLE - Custom component => Popover */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start ">
            <div className="text-left border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                TRAVEL STYLE
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <UserGroupIcon size={20} />
                <p className="text-default-1000 text-sm font-medium">
                  What's your style
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <TagSelector />
        </PopoverContent>
      </Popover>

      {/* TOTAL TRAVELERS - Custom component => Popover */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start">
            <div className="text-left border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                TOTAL TRAVELERS
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <PaletteIcon size={20} />
                <p className="text-default-1000 text-sm font-medium">
                  Solo or Group
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <AgeSelector />
        </PopoverContent>
      </Popover>

      {/* BUDGET - Custom component => Popover */}
      <Popover placement="bottom-start">
        <PopoverTrigger>
          <Button variant="light" className="h-auto p-2 pl-0 justify-start">
            <div className="text-left border-l-2 pl-4">
              <p className="text-lightGray text-base font-medium opacity-50 uppercase">
                BUDGET
              </p>
              <div className="flex items-center space-x-2 mt-1.5">
                <BankNoteIcon size={20} />
                <p className="text-default-1000 text-sm font-medium">
                  Total estimate
                </p>
              </div>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="mt-1">
          <PriceRangeSlider />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default PrimaryFilter;
