.sidebar-wrapper {
  width: 72px;
  height: 100vh;
  background-color: #f1f2f6;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  will-change: width;
}

.sidebar-wrapper.expanded {
  width: 260px;
}

.sidebar-wrapper.mobile .sidebar-toggle-btn {
  position: absolute;
  top: 1px;
  right: -48px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px;
  z-index: 10;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e1e2f;
}

.sidebar-welcome {
  opacity: 0;
  height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(-10px);
}

.sidebar-welcome.show {
  opacity: 1;
  height: auto;
  margin-top: 0.5rem;
  transform: translateY(0);
}

.sidebar-welcome h4 {
  font-size: 1rem;
  font-weight: bold;
  margin: 0;
}

.sidebar-welcome p {
  font-size: 0.75rem;
  color: #7a7a8c;
  margin: 0.25rem 0 0;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
  padding: 0 1rem;
  gap: 0.25rem;
  flex: 1;
  margin-top: 1rem;
}

.sidebar-section {
  font-size: 0.7rem;
  font-weight: 500;
  color: #7a7a8c;
  margin: 1rem 0 0.25rem 0.25rem;
  text-transform: uppercase;
}

.sidebar-link.recommend {
  /* border: 2px solid #707ff5; */
  background: #f6f7ff;
  height: 50px;
  min-height: 50px;
}

.sidebar-profile {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.sidebar-profile-img {
  border-radius: 50%;
}

.sidebar-profile-info {
  display: flex;
  flex-direction: column;
}

.sidebar-name {
  font-weight: 600;
  font-size: 0.85rem;
}

.sidebar-username {
  font-size: 0.7rem;
  color: #7a7a8c;
}

/* Add transition for label (text) reveal */
.sidebar-link span {
  opacity: 0;
  transform: translateX(-15px);
  transition:
    opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transition-delay: 0.1s;
  white-space: nowrap;
}

/* Show label on expand with smooth transition */
.sidebar-wrapper.expanded .sidebar-link span {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.15s;
}

/* Center icon inside a square */
.sidebar-link .icon-wrapper {
  /* width: 50px; */
  height: 50px;
  /* min-width: 50px; */
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sidebar link alignments */
.sidebar-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.65rem 1rem;
  height: 50px;
  min-height: 50px;
  text-decoration: none;
  color: #1e1e2f;
  border-radius: 20px;
  transition:
    background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: background-color, transform;
}

.sidebar-link:hover {
  transform: translateX(2px);
}

.sidebar-link svg {
  font-size: 20px;
}

.sidebar-wrapper.mobile.collapsed {
  transform: translateX(-100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 50;
}

.sidebar-wrapper.mobile.expanded {
  transform: translateX(0);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 50;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
}

/* Prevent text selection during animations */
.sidebar-wrapper * {
  user-select: none;
}

/* Smooth scrolling for sidebar content */
.sidebar-wrapper {
  scroll-behavior: smooth;
}

/* Add subtle shadow when expanded */
.sidebar-wrapper.expanded {
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  transition:
    width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Text visibility handling without layout shift */
.logo-text,
.label-text,
.sidebar-section,
.sidebar-profile-info {
  opacity: 0;
  visibility: hidden;
  transform: translateX(-10px);
  transition:
    opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    visibility 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transition-delay: 0.05s;
  white-space: nowrap;
}

.sidebar-wrapper.expanded .logo-text,
.sidebar-wrapper.expanded .label-text,
.sidebar-wrapper.expanded .sidebar-section,
.sidebar-wrapper.expanded .sidebar-profile-info {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  transition-delay: 0.1s;
}

/* Staggered animation for menu items */
.sidebar-wrapper.expanded .sidebar-link:nth-child(1) span {
  transition-delay: 0.1s;
}

.sidebar-wrapper.expanded .sidebar-link:nth-child(2) span {
  transition-delay: 0.15s;
}

.sidebar-wrapper.expanded .sidebar-link:nth-child(3) span {
  transition-delay: 0.2s;
}

.sidebar-wrapper.expanded .sidebar-link:nth-child(4) span {
  transition-delay: 0.25s;
}

.sidebar-wrapper.expanded .sidebar-link:nth-child(5) span {
  transition-delay: 0.3s;
}

/* Icon wrapper smooth transitions */
.sidebar-link .icon-wrapper {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-link:hover .icon-wrapper {
  transform: scale(1.1);
}
